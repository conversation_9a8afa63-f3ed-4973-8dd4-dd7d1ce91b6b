<script setup lang="ts">
import { format, isToday } from 'date-fns'
import { RecycleScroller } from 'vue-virtual-scroller'
import LogsListLoading from './LogsListLoading.vue'

const toast = useToast()

const props = defineProps({
  modelValue: {
    type: Object as PropType<any | null>,
    default: null
  },
  logs: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  allRelatedLogs: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  logsFilterKeyword: {
    type: String,
    default: ''
  },
  loading: {
    type: <PERSON>olean,
    default: false
  },
  mode: {
    type: Number,
    default: 0
  },
  selectedLogs: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})

const emit = defineEmits([
  'update:modelValue',
  'exportSelectedLog',
  'update:selectedLogs'
])

const logsRefs = ref<Element[]>([])

const selectedMail = computed({
  get() {
    return props.modelValue
  },
  set(value: any | null) {
    emit('update:modelValue', value)
  }
})

watch(selectedMail, () => {
  if (!selectedMail.value) {
    return
  }

  const ref = logsRefs.value[selectedMail.value.request_id]
  if (ref) {
    ref.scrollIntoView({ block: 'nearest' })
  }
})

defineShortcuts({
  arrowdown: () => {
    const index = props.logs.findIndex(
      log => log.request_id === selectedMail.value?.request_id
    )

    if (index === -1) {
      selectedMail.value = props.logs[0]
    } else if (index < props.logs.length - 1) {
      selectedMail.value = props.logs[index + 1]
    }
  },
  arrowup: () => {
    const index = props.logs.findIndex(
      log => log.request_id === selectedMail.value?.request_id
    )

    if (index === -1) {
      selectedMail.value = props.logs[props.logs.length - 1]
    } else if (index > 0) {
      selectedMail.value = props.logs[index - 1]
    }
  }
})

const highlight = (text: string, keyword: string) => {
  if (!keyword) {
    return text
  }

  const regex = new RegExp(`(${keyword})`, 'gi')
  const newText = text.replace(
    regex,
    '<span class="bg-orange-500 dark:bg-orange-600 text-gray-100">$1</span>'
  )

  // if highlight in text is too long, cut head, make from 0 to first highlight word in text to be ... + highlight word
  if (newText.length > 20) {
    const firstHighlightIndex = newText.indexOf('<span')
    const firstHighlightWord = newText.slice(
      firstHighlightIndex,
      newText.indexOf('</span>') + 7
    )
    // return before first highlight word + ... + first highlight word
    return (
      newText.slice(0, firstHighlightIndex)
      + firstHighlightWord
      + newText.slice(firstHighlightIndex + firstHighlightWord.length)
    )
  }

  return newText
}
const checkLogType = (log: any) => {
  if (props.mode === 0) {
    return props.selectedLogs.some(
      selected => selected.request_id === log.request_id
    )
  } else {
    return props.selectedLogs.some(
      selected =>
        selected.session_id === log.session_id
        && selected.request_id === log.request_id
    )
  }
}

const handleSelectedLogs = (log: any, checked: boolean) => {
  if (props.mode === 1) {
    const logsWithSameSession = props.allRelatedLogs.filter(
      l => l.session_id === log.session_id
    )

    const selectedSessionLogs = checked
      ? [...props.selectedLogs, ...logsWithSameSession]
      : props.selectedLogs.filter(
          selectedLog => selectedLog.session_id !== log.session_id
        )

    emit('update:selectedLogs', selectedSessionLogs)
  } else {
    const selectedMessageLogs = checked
      ? [...props.selectedLogs, log]
      : props.selectedLogs.filter(
          selectedLog => selectedLog.request_id !== log.request_id
        )

    emit('update:selectedLogs', selectedMessageLogs)
  }
}

// Function to copy ID to clipboard
const copyToClipboard = (text: string, type: string) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      toast.add({
        title: `${type}をコピーしました`,
        icon: 'i-heroicons-check-circle',
        color: 'green',
        timeout: 2000
      })
    })
    .catch(() => {
      toast.add({
        title: 'コピーに失敗しました',
        icon: 'i-heroicons-exclamation-circle',
        color: 'red',
        timeout: 2000
      })
    })
}
</script>

<template>
  <UDashboardPanelContent class="p-0 scrollbar-thin">
    <LogsListLoading
      v-if="loading"
      loading
    />
    <div
      v-else-if="!logs.length"
      class="flex flex-col gap-3 items-center justify-center h-full"
    >
      <UIcon
        name="ix:box-open"
        class="text-7xl text-gray-500 dark:text-gray-600"
      />
      <p class="text-gray-500 dark:text-gray-600">
        ログが見つかりませんでした。
      </p>
    </div>
    <RecycleScroller
      v-else
      v-slot="{ item: log }"
      class="scroller"
      :items="logs"
      :item-size="156"
      key-field="request_id"
    >
      <div
        :ref="el => { logsRefs[log.request_id] = el as Element }"
        class="group/log-row h-[156px] relative py-0 text-sm cursor-pointer border-l-2 text-gray-600 dark:text-gray-300 w-full"
        :class="[
          selectedMail && selectedMail.request_id === log.request_id
            ? 'border-primary-500 dark:border-primary-400 bg-primary-100 dark:bg-primary-900/25'
            : 'border-white dark:border-gray-900 hover:border-primary-500/25 dark:hover:border-primary-400/25 hover:bg-primary-100/50 dark:hover:bg-primary-900/10'
        ]"
        @click="selectedMail = log"
      >
        <div class="flex gap-0 items-center justify-between h-full">
          <div
            class="group/check transition-all px-4 duration-300 group-hover/log-row:ml-0 h-full flex items-center hover:bg-opacity-65"
            :class="{
              'ml-0': checkLogType(log),
              '-ml-10': !checkLogType(log)
            }"
            @click="handleSelectedLogs(log, !checkLogType(log))"
          >
            <UCheckbox
              color="primary"
              :model-value="checkLogType(log)"
              class="group-hover/check:scale-150 transition-all duration-200"
            />
          </div>

          <div class="w-full h-full flex flex-col py-2">
            <div class="flex items-center justify-between">
              <!-- ID display with hover-to-copy functionality -->
              <div
                class="group/id flex items-center gap-2 -ml-2 px-2 py-0 rounded-full hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                @click="
                  copyToClipboard(
                    mode === 1 ? log.session_id : log.request_id,
                    mode === 1 ? 'セッションID' : 'リクエストID'
                  )
                "
              >
                <div class="flex flex-row items-center gap-1 flex-1 min-w-0">
                  <div class="text-[10px] text-gray-500 dark:text-gray-400">
                    {{ mode === 1 ? "セッションID" : "リクエストID" }}:
                  </div>
                  <div
                    class="text-xs font-mono truncate text-gray-500 dark:text-gray-500"
                  >
                    {{ mode === 1 ? log.session_id : log.request_id }}
                  </div>
                </div>
                <UButton
                  icon="i-heroicons-clipboard-document"
                  color="gray"
                  variant="ghost"
                  size="xs"
                  class="opacity-0 group-hover/id:opacity-100 transition-opacity"
                />
              </div>

              <div class="text-gray-500 dark:text-gray-500 min-w-16">
                {{
                  isToday(new Date(log.query_created_at))
                    ? format(new Date(log.query_created_at), "HH:mm")
                    : format(new Date(log.query_created_at), "MM月dd日")
                }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div
                class="line-clamp-1 font-semibold"
                v-html="highlight(log.query, logsFilterKeyword)"
              />
            </div>

            <p
              class="text-gray-500 dark:text-gray-500 line-clamp-2 text-xs mt-2"
              v-html="highlight(log.answer, logsFilterKeyword)"
            />
            <div class="-space-y-1 mt-auto">
              <BaseTokenInfoTag
                :prompt-tokens="log.prompt_tokens"
                :completion-tokens="log.completion_tokens"
                :token-count="log.token_count"
                :session-id="log.session_id"
                :is-session-mode="mode === 1"
                class="mt-2"
              />
              <BaseAnalysisInfoTag
                :analyzed-action="log.analyzed_action"
                :context-type="log.context_type"
                :user-type="log.user_type"
                :username="log.username"
                :session-id="log.session_id"
                :is-session-mode="mode === 1"
              />
            </div>
          </div>
          <div
            class="group-hover/log-row:block hidden absolute right-2 bottom-2"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:download"
              size="xs"
              :loading="false"
              label="CSV出力"
              @click="emit('exportSelectedLog', log)"
            />
          </div>
        </div>
      </div>
      <UDivider />
    </RecycleScroller>
  </UDashboardPanelContent>
</template>
