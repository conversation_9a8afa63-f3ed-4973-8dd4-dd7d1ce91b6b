import { createSharedComposable } from '@vueuse/core'

const _useNavigators = () => {
  const { selectedTenantId, selectedEnvId } = useApp()
  const route = useRoute()
  const tenantId = computed(() => route.params.tenantId)
  const env = computed(() => route.params.env)
  const { isPreviewSlideoverOpen } = useDashboard()
  const trainingDatasStore = useTrainingDatasStore()

  const {
    trainingDatasTotal,
    trainingDatasNavigatorsTopFive,
    hasAnyUnreflected
  } = storeToRefs(trainingDatasStore)

  const { canUseFeature, previewBadge } = useFeatures()

  const basicSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings',
        label: 'チャットボット設定',
        to: '/settings/',
        icon: withIcon && 'fluent:bot-28-regular',
        exact: true
      },
      {
        id: 'tenantId-env-settings-error-messages',
        label: 'エラーメッセージ',
        to: '/settings/error-messages',
        icon: withIcon && 'ri:chat-settings-line'
      },
      {
        id: 'tenantId-env-settings-survey',
        label: 'アンケート',
        to: '/settings/survey',
        icon: withIcon && 'wpf:survey'
      }
      // {
      //   id: 'tenantId-env-settings-chat-interface',
      //   label: 'インターフェース',
      //   to: '/settings/chat-interface',
      //   icon: withIcon && 'fluent:slide-text-sparkle-20-regular'
      // },
      // {
      //   id: 'tenantId-env-settings-security',
      //   label: 'セキュリティ',
      //   to: '/settings/security',
      //   icon: withIcon && 'material-symbols:security'
      // },
      // {
      //   id: 'tenantId-env-settings-prompts',
      //   label: 'プロンプト特殊指示',
      //   to: '/settings/prompts',
      //   icon: withIcon && 'fluent:prompt-20-regular'
      // }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const settingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-basic',
        label: '基本設定',
        to: '/settings/basic',
        icon: withIcon && 'ri:chat-settings-line'
      },
      {
        id: 'tenantId-env-settings-users',
        label: 'ユーザ管理',
        to: '/settings/users',
        icon: withIcon && 'la:users-cog'
      },
      {
        id: 'tenantId-env-settings-labels',
        label: 'ラベル管理',
        to: '/settings/labels',
        icon: withIcon && 'pajamas:labels'
      },
      {
        id: 'tenantId-env-settings-categories',
        label: 'カテゴリ管理',
        to: '/settings/categories',
        icon: withIcon && 'carbon:category-new-each'
      },
      {
        id: 'tenantId-env-settings-prompts',
        label: 'プロンプト特殊指示',
        to: '/settings/prompts',
        icon: withIcon && 'fluent:prompt-20-regular'
      },
      {
        id: 'tenantId-env-settings-system',
        label: 'システム設定',
        to: '/settings/system',
        icon: withIcon && 'tdesign:system-setting-filled'
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const adminSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-users',
        label: 'ユーザ管理',
        to: '/settings/users/',
        icon: withIcon && 'la:users-cog'
      },
      {
        id: 'tenantId-env-settings-user-groups',
        label: 'ユーザグループ管理',
        to: '/settings/user-groups/',
        icon: withIcon && 'material-symbols-light:groups'
      },
      // {
      //   id: 'tenantId-env-settings-ip-locks',
      //   label: 'IPアドレスロック',
      //   to: '/settings/ip-lock/',
      //   icon: withIcon && 'material-symbols-light:lock'
      // },
      {
        id: 'tenantId-env-settings-page-permissions',
        label: 'ページ権限管理',
        description: 'ロール別のページアクセス権限を管理します',
        to: '/settings/page-permissions/',
        icon: withIcon && 'i-heroicons-lock-closed'
      },
      {
        id: 'tenantId-env-settings-ip-address-control',
        label: 'IPアドレス制御',
        description: 'IPアドレスの制御設定を管理します',
        to: '/settings/ip-address-control/',
        icon: withIcon && 'material-symbols:security',
        hide: !canUseFeature('ipAddressControl'),
        badge: previewBadge('ipAddressControl')
      }
    ]
      .filter(nav => !nav.hide)
      .map(nav => ({
        ...nav,
        to: tenantId.value
          ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
          : nav.to
      }))
  })

  const knowledgeSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-labels',
        label: 'ラベル管理',
        to: '/settings/labels/',
        icon: withIcon && 'quill:label'
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const ragSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-feature-settings',
        label: 'RAG設定管理',
        to: '/settings/feature-settings/',
        icon: withIcon && 'carbon:settings-services'
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const pnlOperatorSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-tenants',
        label: 'テナント管理',
        to: '/settings/tenants/',
        icon: withIcon && 'octicon:organization-24'
      },
      {
        id: 'tenantId-env-settings-operator-users',
        label: 'PNL管理者ユーザ',
        to: '/settings/operator-users/',
        icon: withIcon && 'dashicons:superhero-alt'
      },
      {
        id: 'tenantId-env-settings-api-action-logs',
        label: 'API操作ログ',
        to: '/settings/api-action-logs/',
        icon: withIcon && 'mdi:api'
      },
      {
        id: 'tenantId-env-settings-feature-settings',
        label: 'RAG設定管理',
        to: '/settings/feature-settings/',
        icon: withIcon && 'carbon:settings-services'
      },
      {
        id: 'tenantId-env-settings-system',
        label: 'システム設定',
        to: '/settings/system/',
        icon: withIcon && 'tdesign:system-setting-filled'
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const statisticsSettingsNavigators = computed(() => (withIcon = true) => {
    return [
      {
        id: 'tenantId-env-settings-categories',
        label: 'カテゴリ管理',
        to: '/settings/categories/',
        icon: withIcon && 'carbon:category-new-each'
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const statisticsNavigators = computed(() => {
    return [
      {
        id: 'statistics',
        label: '統計',
        icon: 'hugeicons:dashboard-browsing',
        to: '/',
        tooltip: {
          text: '統計'
        },
        exact: true,
        children: [
          {
            type: 'tenantId-env-statistics-dashboard',
            label: 'ダッシュボード',
            to: '/',
            exact: true
          },
          {
            type: 'tenantId-env-statistics-surveys',
            label: '回答後のアンケート',
            to: '/statistics/surveys/'
          },
          {
            type: 'tenantId-env-statistics-unanswered',
            label: '未回答の質問',
            to: '/statistics/unanswered-questions/'
          }
        ].map(nav => ({
          ...nav,
          to: tenantId.value
            ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
            : nav.to
        }))
      },
      {
        id: 'logs',
        label: 'ログ情報',
        icon: 'mdi:math-log',
        to: '/logs/',
        tooltip: {
          text: 'ログ情報'
        }
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const settingsGroupNavigators = computed(() => {
    return [
      {
        id: 'settings',
        label: '設定',
        to: '/settings',
        icon: 'i-heroicons-cog-8-tooth',
        // children: settingsNavigators.value(false),
        tooltip: {
          text: '設定'
        }
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const trainingDataNavigators = computed(() => {
    return [
      {
        id: 'training-data',
        label: 'データソース',
        icon: hasAnyUnreflected.value
          ? 'fluent-color:warning-16'
          : 'iconoir:learning',
        click: () =>
          navigateTo(
            `/${selectedTenantId.value}/${selectedEnvId.value}/training-data`
          ),
        tooltip: {
          text: 'データソース'
        },
        collapsible: false,
        badge: trainingDatasTotal.value,
        children: [
          ...trainingDatasNavigatorsTopFive.value,
          {
            id: 'tenantId-env-training-data',
            label: `+${trainingDatasTotal.value - 5}件 (全て表示)`,
            icon: 'material-symbols-light:more-outline',
            to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data`,
            active: false,
            hide: trainingDatasTotal.value <= 5,
            labelClass: 'text-gray-500 text-xs'
          }
        ].filter(nav => !nav.hide),
        active: route.name === 'tenantId-env-training-data'
      },
      {
        id: 'indexer-history',
        label: 'インデックス更新履歴',
        icon: 'ic:baseline-history',
        tooltip: {
          text: 'インデックス更新履歴'
        },
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/indexer-history/`
      }
    ].map(nav => ({
      ...nav
    }))
  })

  const footerNavigators = computed(() => {
    return [
      {
        id: 'settings',
        label: '設定',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/settings/`,
        icon: 'i-heroicons-cog-8-tooth',
        tooltip: {
          text: '設定'
        }
      }
      // {
      //   slot: 'preview',
      //   label: 'プレビュー',
      //   icon: 'hugeicons:message-preview-01',
      //   tooltip: {
      //     text: 'プレビュー',
      //   },
      //   click: () => (isPreviewSlideoverOpen.value = true)
      // }
    ]
  })

  const connectNavigators = computed(() => {
    return [
      {
        id: 'settings',
        label: 'コネクト',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/connects/`,
        icon: 'gridicons:share-computer'
      }
    ]
  })

  const connectOptionsNavigators = computed(() => {
    return [
      {
        id: 'tenantId-env-embed',
        label: '埋め込み',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/connects/`,
        icon: 'fluent-mdl2:embed',
        exact: true
      },
      {
        id: 'tenantId-env-liff',
        label: 'LINE LIFF',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/connects/liff`,
        icon: 'mingcute:line-app-fill'
      }
      // {
      //   id: 'tenantId-env-embed-share',
      //   label: 'シェア',
      //   to: `/${selectedTenantId.value}/${selectedEnvId.value}/connects/share`,
      //   icon: 'fluent:share-16-regular'
      // },
      // {
      //   id: 'tenantId-env-embed-integrations',
      //   label: '統合',
      //   to: `/${selectedTenantId.value}/${selectedEnvId.value}/connects/integrations`,
      //   icon: 'carbon:ibm-cloud-pak-integration'
      // }
    ]
  })

  const supportNavigators = computed(() => {
    return [
      {
        id: 'support',
        label: 'サポート',
        to: `/support`,
        icon: 'material-symbols:help-outline',
        tooltip: {
          text: 'サポート'
        }
      }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  const supportOptionsNavigators = computed(() => {
    return [
      {
        id: 'tenantId-env-support-tours',
        label: 'ガイドツアー',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/support/tours`,
        icon: 'streamline:manual-book-remix'
      },
      {
        id: 'tenantId-env-support-faq',
        label: 'よくある質問',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/support/faq`,
        icon: 'material-symbols:quiz-outline'
      },
      {
        id: 'tenantId-env-support-manual',
        label: '操作マニュアル',
        icon: 'material-symbols:menu-book-outline',
        external: true,
        target: '_blank',
        to: 'https://playnext-lab.notion.site/1f384d393a9c805b8944fce3560616f9'
      },
      {
        id: 'tenantId-env-support-contact',
        label: 'お問い合わせ',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/support/contact`,
        icon: 'material-symbols:contact-support-outline'
      }
    ]
  })

  const deploymentNavigators = computed(() => {
    return [
      {
        id: 'tenantId-env-settings-deployment',
        label: 'デプロイ',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/settings/deployment/`,
        icon: 'material-symbols:deployed-code-alert'
      },
      {
        id: 'tenantId-env-settings-deployment-logs',
        label: 'デプロイ履歴',
        icon: 'ic:baseline-history',
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/settings/deployment-logs`
      }
    ]
  })

  const trainingDataTypeNavigators = computed(() => {
    return [
      {
        label: 'ファイル',
        value: 'file',
        icon: 'hugeicons:files-01',
        to: '/training-data/new/',
        exact: true
      },
      {
        label: 'ウェブサイト',
        value: 'website',
        icon: 'mdi:web',
        to: '/training-data/new/web'
      }
      // {
      //   label: 'テキスト',
      //   value: 'text',
      //   icon: 'ion:language',
      //   to: '/training-data/new/text'
      // },
      // {
      //   label: 'Q&A',
      //   value: 'qa',
      //   icon: 'ix:language',
      //   to: '/training-data/new/qa'
      // }
    ].map(nav => ({
      ...nav,
      to: tenantId.value
        ? `/${selectedTenantId.value}/${selectedEnvId.value}${nav.to}`
        : nav.to
    }))
  })

  return {
    statisticsNavigators,
    settingsNavigators,
    footerNavigators,
    trainingDataNavigators,
    settingsGroupNavigators,
    trainingDataTypeNavigators,
    basicSettingsNavigators,
    knowledgeSettingsNavigators,
    statisticsSettingsNavigators,
    adminSettingsNavigators,
    deploymentNavigators,
    connectNavigators,
    connectOptionsNavigators,
    supportNavigators,
    supportOptionsNavigators,
    ragSettingsNavigators,
    pnlOperatorSettingsNavigators
  }
}

export const useNavigators = createSharedComposable(_useNavigators)
