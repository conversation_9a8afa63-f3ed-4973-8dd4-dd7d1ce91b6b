<script setup lang="ts">
import { cloneDeep } from '@unovis/ts'
import { z } from 'zod'

const { selectedTenantId, selectedEnvId } = useApp()
const categoryStore = useCategoriesStore()
const { categoriesForDropDown } = storeToRefs(categoryStore)

// UUID validation regex
const uuidRegex
  = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

// Zod schema for logs advanced search form
const logsAdvancedSearchSchema = z.object({
  range: z.object({
    start: z.date(),
    end: z.date()
  }),
  session_id: z
    .string()
    .optional()
    .nullable()
    .transform(val => val?.trim() || undefined)
    .refine(val => !val || uuidRegex.test(val), {
      message: 'セッションIDは有効なUUID形式である必要があります'
    }),
  request_id: z
    .string()
    .optional()
    .transform(val => val?.trim() || undefined)
    .refine(val => !val || uuidRegex.test(val), {
      message: 'リクエストIDは有効なUUID形式である必要があります'
    }),
  category_id: z.any().optional(),
  query: z
    .string()
    .optional()
    .transform(val => val?.trim() || undefined),
  answer: z
    .string()
    .optional()
    .transform(val => val?.trim() || undefined),
  context_type: z.any().optional(),
  analyzed_action: z.any().optional(),
  processed: z.any().optional()
})

const props = defineProps({
  show: Boolean,
  conditions: {
    type: Object,
    default: () => ({})
  }
})
const toast = useToast()

// Initialize with default date range (last 3 days)
const today = new Date()
const threeDaysAgo = new Date()
threeDaysAgo.setDate(today.getDate() - 3)

const logsAdvancedSearch = ref({
  range: {
    start: threeDaysAgo,
    end: today
  },
  session_id: '',
  request_id: '',
  category_id: null as any,
  query: '',
  answer: '',
  context_type: null as any,
  analyzed_action: null as any,
  processed: null as any
})

enum ContextType {
  UNKNOWN = 0,
  KNOWLEDGE = 1,
  WEBSEARCH = 2,
  NOKNOWLEDGEMATCH = 99
}

// Options for context types
const contextTypes = [
  { label: '', value: null },
  { label: '不明', value: ContextType.UNKNOWN },
  { label: 'ナレッジデータベース', value: ContextType.KNOWLEDGE },
  { label: 'ウエブ検索', value: ContextType.WEBSEARCH },
  { label: '見つからない', value: ContextType.NOKNOWLEDGEMATCH }
]

enum LlmAnalyzeNextAction {
  NORMAL = 1,
  RAG = 2,
  SUMMARY = 3,
  WEATHER = 101,
  CACHE = 199
}

// Options for analyzed action types
const defaultAnalyzedActionType = [
  { label: '', value: null },
  { label: '通常', value: LlmAnalyzeNextAction.NORMAL },
  { label: 'RAG', value: LlmAnalyzeNextAction.RAG },
  { label: '要約', value: LlmAnalyzeNextAction.SUMMARY },
  { label: '天気', value: LlmAnalyzeNextAction.WEATHER },
  { label: 'セマンテックキャッシュ', value: LlmAnalyzeNextAction.CACHE }
]

// Options for processed status
const defaultProcessedType = [
  { label: '', value: null },
  { label: '正常', value: true },
  { label: '失敗', value: false }
]

const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const emit = defineEmits(['close', 'search'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

// Validation function for logs advanced search
const validateLogsAdvancedSearch = () => {
  try {
    logsAdvancedSearchSchema.parse(logsAdvancedSearch.value)
    return { success: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.errors }
    }
    return {
      success: false,
      errors: [{ message: '予期しないエラーが発生しました' }]
    }
  }
}

// Function to perform search
const onSearch = () => {
  const validation = validateLogsAdvancedSearch()

  if (!validation.success) {
    toast.add({
      title: 'エラー',
      description: validation.errors[0]?.message || '検索条件に誤りがあります',
      color: 'red'
    })
    return
  }

  // Emit search event with validated data
  emit('search', logsAdvancedSearch.value)
  isOpen.value = false
}

const isSearchDisabled = computed(() => {
  return !validateLogsAdvancedSearch().success
})

watch(
  () => props.show,
  (value) => {
    if (value) {
      logsAdvancedSearch.value = cloneDeep(props.conditions) as any
    }
  }
)

onMounted(async () => {
  await categoryStore.fetchCategories(selectedTenantId.value, selectedEnvId.value)
})

const onClear = () => {
  logsAdvancedSearch.value = {
    range: {
      start: threeDaysAgo,
      end: today
    },
    session_id: '',
    request_id: '',
    category_id: null as any,
    query: '',
    answer: '',
    context_type: null as any,
    analyzed_action: null as any,
    processed: null as any
  }
}
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>高度な検索</div>
        </div>
      </template>

      <div class="flex flex-col gap-5 px-4 py-6">
        <!-- Advanced Search Form -->
        <UForm
          :schema="logsAdvancedSearchSchema"
          :state="logsAdvancedSearch"
          class="space-y-4"
        >
          <UFormGroup
            name="range"
            label="日付"
            description="日付範囲を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseDateRangePicker
              v-model="logsAdvancedSearch.range"
              :exclude-today="true"
              color="white"
              variant="solid"
              size="sm"
              class="w-full"
              icon="i-heroicons-calendar-20-solid"
            />
          </UFormGroup>

          <UFormGroup
            name="session_id"
            label="セッションID"
            description="フルセッションIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="logsAdvancedSearch.session_id"
              data-tour="advanced-search-session-id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="request_id"
            label="リクエストID"
            description="リクエストIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="logsAdvancedSearch.request_id"
              data-tour="advanced-search-request-id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="category_id"
            label="カテゴリ"
            description="カテゴリを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelectMenu
              v-model="logsAdvancedSearch.category_id"
              data-tour="advanced-search-category"
              icon="material-symbols:category-outline"
              placeholder="カテゴリ名"
              class="w-full"
              :options="categoriesForDropDown"
              :ui-menu="{ option: { base: 'capitalize' } }"
              value-attribute="value"
            >
              <template
                v-if="logsAdvancedSearch.category_id"
                #trailing
              >
                <UButton
                  color="gray"
                  variant="link"
                  size="xs"
                  :padded="false"
                  icon="i-heroicons-x-mark-20-solid"
                  @click="
                    (e) => {
                      e.stopPropagation();
                      logsAdvancedSearch.category_id = null;
                    }
                  "
                />
              </template>
            </USelectMenu>
          </UFormGroup>

          <UFormGroup
            name="query"
            label="質問"
            description="質問内容を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <UInput
              v-model="logsAdvancedSearch.query"
              data-tour="advanced-search-query"
              icon="mingcute:search-3-line"
              size="sm"
              placeholder="質問内容..."
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="answer"
            label="回答"
            description="回答内容を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <UInput
              v-model="logsAdvancedSearch.answer"
              data-tour="advanced-search-answer"
              icon="mingcute:search-3-line"
              size="sm"
              placeholder="回答内容..."
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="context_type"
            label="コンテキスト"
            description="コンテキストの種類を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="logsAdvancedSearch.context_type"
              data-tour="advanced-search-context-type"
              :options="contextTypes"
              option-attribute="label"
              value-attribute="value"
              placeholder="コンテキストを選択"
              size="sm"
            />
          </UFormGroup>

          <UFormGroup
            name="analyzed_action"
            label="分析アクション"
            description="分析アクションを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="logsAdvancedSearch.analyzed_action"
              data-tour="advanced-search-analyzed-action"
              :options="defaultAnalyzedActionType"
              option-attribute="label"
              value-attribute="value"
              placeholder="分析アクションを選択"
              size="sm"
            />
          </UFormGroup>

          <UFormGroup
            name="processed"
            label="正常処理済み"
            description="正常処理済みの状態を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="logsAdvancedSearch.processed"
              data-tour="advanced-search-processed"
              :options="defaultProcessedType"
              option-attribute="label"
              value-attribute="value"
              placeholder="処理状態を選択"
              size="sm"
            />
          </UFormGroup>
        </UForm>
      </div>
      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div>
            <UButton
              label="キャンセル"
              color="gray"
              @click="emit('close')"
            />
          </div>
          <div class="flex justify-end gap-3">
            <UButton
              label="条件クリア"
              color="white"
              @click="onClear"
            />
            <UButton
              label="検索"
              color="primary"
              class="w-24 justify-center"
              :disabled="isSearchDisabled"
              @click="onSearch"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
