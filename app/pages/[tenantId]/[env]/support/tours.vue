<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()

const toursStore = useToursStore()

const tourCategories = [
  {
    key: 'welcome',
    label: 'はじめに',
    icon: 'material-symbols:waving-hand',
    children: [
      {
        key: 'tour001-welcome',
        label: 'ウェルカムツアー',
        description: 'システムの基本機能と使い方を学びます',
        click: () => toursStore.showWelcomeTour()
      }
    ]
  },
  {
    key: 'auth',
    label: '認証',
    icon: 'material-symbols:login',
    children: [
      {
        key: 'tour002-auth',
        label: 'ログイン',
        description: 'システムへのログイン方法を学びます',
        click: () => toursStore.showLoginTour()
      },
      {
        key: 'tour003-new-password',
        label: '初期ログイン（新パスワード設定）',
        description: '初回ログイン時のパスワード設定方法',
        click: () => toursStore.showFirstLoginTour()
      },
      {
        key: 'tour004-forgot-password',
        label: 'パスワードリセット',
        description: 'パスワードを忘れた場合のリセット方法',
        click: () => toursStore.showForgotPasswordTour()
      },
      {
        key: 'tour005-change-password',
        label: 'パスワード変更',
        description: 'ログイン後のパスワード変更方法',
        click: () => toursStore.showChangePasswordTour()
      }
    ]
  },
  {
    key: 'dashboard',
    label: '統計・ダッシュボード',
    icon: 'hugeicons:dashboard-browsing',
    children: [
      {
        key: 'tour007-dashboard',
        label: 'ダッシュボード',
        description: 'メインダッシュボードの使い方',
        click: async () => toursStore.showDashboardTour()
      },
      {
        key: 'tour008-surveys',
        label: '回答後のアンケート',
        description: 'アンケート結果の確認方法',
        click: async () => toursStore.showSurveysTour()
      },
      {
        key: 'tour009-unanswered',
        label: '未回答の質問',
        description: '回答できなかった質問の確認',
        click: async () => {}
      },
      {
        key: 'tour010-logs',
        label: 'ログ情報',
        description: 'システムログの確認方法',
        click: async () => {}
      }
    ]
  },
  {
    key: 'datasource',
    label: 'データソース',
    icon: 'hugeicons:database-02',
    children: [
      {
        key: 'tour011-datasource-list',
        label: 'データソース一覧',
        description: '登録されたデータソースの管理',
        click: async () => {}
      },
      {
        key: 'tour012-datasource-register',
        label: 'データソース登録',
        description: '新しいデータソースの登録方法',
        click: async () => {}
      },
      {
        key: 'tour013-knowledge-list',
        label: 'ナレッジ一覧',
        description: 'ナレッジデータの一覧表示',
        click: async () => {}
      },
      {
        key: 'tour014-knowledge-detail',
        label: 'ナレッジ詳細',
        description: '個別ナレッジの詳細確認',
        click: async () => {}
      },
      {
        key: 'tour015-index-history',
        label: 'インデックス更新履歴',
        description: 'データインデックスの更新履歴',
        click: async () => {}
      },
      {
        key: 'tour016-connect',
        label: 'コネクト',
        description: '外部システムとの連携設定',
        click: async () => {}
      }
    ]
  },
  {
    key: 'settings',
    label: '設定',
    icon: 'i-heroicons-cog-8-tooth',
    children: [
      {
        key: 'basic-settings',
        label: '基本設定',
        icon: 'fluent:settings-24-regular',
        children: [
          {
            key: 'tour017-chatbot-settings',
            label: 'チャットボット設定',
            description: 'チャットボットの基本設定',
            click: async () => toursStore.showChatbotSettingsTour()
          },
          {
            key: 'tour018-error-message-settings',
            label: 'エラーメッセージ設定',
            description: 'エラーメッセージのカスタマイズ',
            click: async () => toursStore.showErrorMessageSettingsTour()
          },
          {
            key: 'tour019-survey-settings',
            label: 'アンケート設定',
            description: 'アンケート機能の設定',
            click: async () => toursStore.showSurveySettingsTour()
          }
        ]
      },
      {
        key: 'knowledge-settings',
        label: 'ナレッジ設定',
        icon: 'hugeicons:knowledge-01',
        children: [
          {
            key: 'tour020-label-management',
            label: 'ラベル管理',
            description: 'ナレッジラベルの管理',
            click: async () => {}
          }
        ]
      },
      {
        key: 'statistics-settings',
        label: '統計設定',
        icon: 'material-symbols:bar-chart',
        children: [
          {
            key: 'tour021-category-management',
            label: 'カテゴリ管理',
            description: '統計カテゴリの管理',
            click: async () => {}
          }
        ]
      },
      {
        key: 'admin-settings',
        label: '管理者設定',
        icon: 'material-symbols:admin-panel-settings',
        children: [
          {
            key: 'tour022-user-management',
            label: 'ユーザ管理',
            description: 'システムユーザーの管理',
            click: async () => {}
          },
          {
            key: 'tour023-user-group-management',
            label: 'ユーザグループ管理',
            description: 'ユーザーグループの管理',
            click: async () => {}
          },
          {
            key: 'tour024-page-permission-management',
            label: 'ページ権限管理',
            description: 'ページアクセス権限の管理',
            click: async () => {}
          },
          {
            key: 'tour025-ip-control',
            label: 'IPアドレス制御',
            description: 'IPアドレスアクセス制御',
            click: async () => {}
          }
        ]
      },
      {
        key: 'deployment',
        label: 'デプロイメント',
        icon: 'material-symbols:deployed-code-alert',
        children: [
          {
            key: 'tour026-deploy',
            label: 'デプロイ',
            description: 'システムのデプロイ手順',
            click: async () => {}
          },
          {
            key: 'tour027-deploy-history',
            label: 'デプロイ履歴',
            description: 'デプロイ履歴の確認',
            click: async () => {}
          }
        ]
      }
    ]
  }
]

const expandedSections = ref(new Set(['welcome']))

const toggleSection = (sectionKey: string) => {
  if (expandedSections.value.has(sectionKey)) {
    expandedSections.value.delete(sectionKey)
  } else {
    expandedSections.value.add(sectionKey)
  }
}

const executeAction = async (item: any) => {
  if (item.click) {
    await item.click()
  }
}
</script>

<template>
  <UDashboardPanelContent class="p-6">
    <div class="space-y-6">
      <div class="text-center mb-8">
        <UIcon
          name="streamline:manual-book-remix"
          class="w-16 h-16 text-primary-500 mx-auto mb-4"
        />
        <h2
          id="test"
          class="text-2xl font-bold text-gray-900 dark:text-white mb-2"
        >
          ガイドツアー
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          システムの使い方を段階的に学べるガイドツアーです。各機能の使い方を実際の画面で確認できます。
        </p>
      </div>

      <div class="space-y-4">
        <div
          v-for="category in tourCategories"
          :key="category.key"
          class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
        >
          <button
            class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 text-left flex items-center justify-between hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            @click="toggleSection(category.key)"
          >
            <div class="flex items-center space-x-3">
              <UIcon
                :name="category.icon"
                class="w-5 h-5 text-gray-600 dark:text-gray-400"
              />
              <span class="font-medium text-gray-900 dark:text-white">
                {{ category.label }}
              </span>
            </div>
            <UIcon
              :name="
                expandedSections.has(category.key)
                  ? 'i-heroicons-chevron-up'
                  : 'i-heroicons-chevron-down'
              "
              class="w-4 h-4 text-gray-500"
            />
          </button>

          <div
            v-if="expandedSections.has(category.key)"
            class="divide-y divide-gray-100 dark:divide-gray-800"
          >
            <template
              v-for="item in category.children"
              :key="item.key"
            >
              <!-- Sub-category with children -->
              <div
                v-if="item.children"
                class="bg-white dark:bg-gray-900"
              >
                <div
                  class="px-4 py-2 bg-gray-25 dark:bg-gray-850 border-b border-gray-100 dark:border-gray-800"
                >
                  <div class="flex items-center space-x-2">
                    <UIcon
                      v-if="item.icon"
                      :name="item.icon"
                      class="w-4 h-4 text-gray-500"
                    />
                    <span
                      class="text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      {{ item.label }}
                    </span>
                  </div>
                </div>
                <div class="space-y-0">
                  <button
                    v-for="subItem in item.children"
                    :key="subItem.key"
                    class="w-full px-6 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors flex items-center justify-between group"
                    @click="executeAction(subItem)"
                  >
                    <div class="flex-1">
                      <div
                        class="font-medium text-gray-900 dark:text-white mb-1"
                      >
                        {{ subItem.label }}
                      </div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ subItem.description }}
                      </div>
                    </div>
                    <UIcon
                      name="i-heroicons-play"
                      class="w-4 h-4 text-primary-500 opacity-0 group-hover:opacity-100 transition-opacity"
                    />
                  </button>
                </div>
              </div>

              <!-- Direct item -->
              <button
                v-else
                class="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors flex items-center justify-between group bg-white dark:bg-gray-900"
                @click="executeAction(item)"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white mb-1">
                    {{ item.label }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ item.description }}
                  </div>
                </div>
                <UIcon
                  name="i-heroicons-play"
                  class="w-4 h-4 text-primary-500 opacity-0 group-hover:opacity-100 transition-opacity"
                />
              </button>
            </template>
          </div>
        </div>
      </div>

      <!-- Other Support Options -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <UCard class="hover:shadow-lg transition-shadow">
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon
                name="material-symbols:quiz-outline"
                class="w-5 h-5 text-blue-500"
              />
              <h4 class="font-semibold">
                よくある質問
              </h4>
            </div>
          </template>
          <div class="space-y-3">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              システムの使い方で困った時は、まずFAQをご確認ください。
            </p>
            <UButton
              :to="`/${selectedTenantId}/${selectedEnvId}/support/faq`"
              color="blue"
              icon="material-symbols:quiz-outline"
              size="sm"
              block
            >
              FAQを見る
            </UButton>
          </div>
        </UCard>

        <UCard class="hover:shadow-lg transition-shadow">
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon
                name="material-symbols:contact-support-outline"
                class="w-5 h-5 text-green-500"
              />
              <h4 class="font-semibold">
                お問い合わせ
              </h4>
            </div>
          </template>
          <div class="space-y-3">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              解決しない問題については、お気軽にお問い合わせください。
            </p>
            <UButton
              :to="`/${selectedTenantId}/${selectedEnvId}/support/contact`"
              color="green"
              icon="material-symbols:contact-support-outline"
              size="sm"
              block
            >
              お問い合わせする
            </UButton>
          </div>
        </UCard>
      </div>

      <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div class="flex items-start space-x-3">
          <UIcon
            name="i-heroicons-information-circle"
            class="w-5 h-5 text-blue-500 mt-0.5"
          />
          <div>
            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-1">
              ガイドツアーについて
            </h4>
            <p class="text-sm text-blue-700 dark:text-blue-300">
              各ガイドツアーをクリックすると、該当ページに移動してインタラクティブなガイドが開始されます。
              画面の指示に従って操作方法を学習できます。
            </p>
          </div>
        </div>
      </div>
    </div>
  </UDashboardPanelContent>
</template>
